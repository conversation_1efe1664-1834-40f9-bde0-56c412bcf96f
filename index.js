const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 9090;

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 创建必要的目录
const UPLOAD_DIR = path.join(__dirname, 'uploads');
const DOWNLOAD_DIR = path.join(__dirname, 'downloads');

// 确保目录存在
fs.ensureDirSync(UPLOAD_DIR);
fs.ensureDirSync(DOWNLOAD_DIR);

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOAD_DIR);
  },
  filename: function (req, file, cb) {
    // 保持原始文件名，如果有重复则添加时间戳
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const timestamp = Date.now();
    cb(null, `${name}_${timestamp}${ext}`);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB 限制
  }
});

// 静态文件服务 - 提供下载目录中的文件
app.use('/files', express.static(DOWNLOAD_DIR));

// 路由定义

// 首页 - 显示简单的文件管理界面
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>文件下载服务</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
            .file-list { list-style: none; padding: 0; }
            .file-item { padding: 10px; margin: 5px 0; background: #f5f5f5; border-radius: 3px; display: flex; justify-content: space-between; align-items: center; }
            .upload-area { border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 10px 0; }
            button { padding: 8px 16px; margin: 5px; cursor: pointer; }
            .download-btn { background: #007bff; color: white; border: none; border-radius: 3px; }
            .delete-btn { background: #dc3545; color: white; border: none; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h1>文件下载服务</h1>
        
        <div class="section">
            <h2>上传文件</h2>
            <form action="/upload" method="post" enctype="multipart/form-data">
                <div class="upload-area">
                    <input type="file" name="file" required multiple>
                    <br><br>
                    <button type="submit">上传文件</button>
                </div>
            </form>
        </div>

        <div class="section">
            <h2>可下载文件</h2>
            <div id="fileList">
                <p>加载中...</p>
            </div>
        </div>

        <script>
            // 加载文件列表
            function loadFiles() {
                fetch('/api/files')
                    .then(response => response.json())
                    .then(files => {
                        const fileList = document.getElementById('fileList');
                        if (files.length === 0) {
                            fileList.innerHTML = '<p>暂无文件</p>';
                            return;
                        }
                        
                        const ul = document.createElement('ul');
                        ul.className = 'file-list';
                        
                        files.forEach(file => {
                            const li = document.createElement('li');
                            li.className = 'file-item';
                            li.innerHTML = \`
                                <span>\${file.name} (\${file.size})</span>
                                <div>
                                    <button class="download-btn" onclick="downloadFile('\${file.name}')">下载</button>
                                    <button class="delete-btn" onclick="deleteFile('\${file.name}')">删除</button>
                                </div>
                            \`;
                            ul.appendChild(li);
                        });
                        
                        fileList.innerHTML = '';
                        fileList.appendChild(ul);
                    })
                    .catch(error => {
                        console.error('Error loading files:', error);
                        document.getElementById('fileList').innerHTML = '<p>加载文件列表失败</p>';
                    });
            }

            // 下载文件
            function downloadFile(filename) {
                window.open('/download/' + encodeURIComponent(filename));
            }

            // 删除文件
            function deleteFile(filename) {
                if (confirm('确定要删除文件 "' + filename + '" 吗？')) {
                    fetch('/api/files/' + encodeURIComponent(filename), {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            loadFiles(); // 重新加载文件列表
                        } else {
                            alert('删除失败: ' + result.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting file:', error);
                        alert('删除失败');
                    });
                }
            }

            // 页面加载时获取文件列表
            loadFiles();
        </script>
    </body>
    </html>
  `);
});

// API: 获取文件列表
app.get('/api/files', async (req, res) => {
  try {
    const files = await fs.readdir(DOWNLOAD_DIR);
    const fileList = await Promise.all(
      files.map(async (filename) => {
        const filePath = path.join(DOWNLOAD_DIR, filename);
        const stats = await fs.stat(filePath);
        return {
          name: filename,
          size: formatFileSize(stats.size),
          sizeBytes: stats.size,
          modified: stats.mtime
        };
      })
    );
    
    // 按修改时间排序（最新的在前）
    fileList.sort((a, b) => new Date(b.modified) - new Date(a.modified));
    
    res.json(fileList);
  } catch (error) {
    console.error('Error reading files:', error);
    res.status(500).json({ error: '读取文件列表失败' });
  }
});

// 文件上传
app.post('/upload', upload.array('file'), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: '没有文件被上传' });
    }

    // 将上传的文件移动到下载目录
    const uploadedFiles = [];
    for (const file of req.files) {
      const sourcePath = file.path;
      const destPath = path.join(DOWNLOAD_DIR, file.filename);
      await fs.move(sourcePath, destPath);
      uploadedFiles.push(file.filename);
    }

    res.redirect('/?uploaded=' + uploadedFiles.length);
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: '文件上传失败' });
  }
});

// 文件下载
app.get('/download/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(DOWNLOAD_DIR, filename);
  
  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: '文件不存在' });
  }

  // 设置下载头
  res.download(filePath, filename, (err) => {
    if (err) {
      console.error('Download error:', err);
      if (!res.headersSent) {
        res.status(500).json({ error: '文件下载失败' });
      }
    }
  });
});

// API: 删除文件
app.delete('/api/files/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(DOWNLOAD_DIR, filename);
    
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({ success: false, message: '文件不存在' });
    }

    await fs.remove(filePath);
    res.json({ success: true, message: '文件删除成功' });
  } catch (error) {
    console.error('Delete error:', error);
    res.status(500).json({ success: false, message: '删除文件失败' });
  }
});

// 工具函数：格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 启动服务器
app.listen(PORT, () => {
  console.log(`文件下载服务已启动`);
  console.log(`访问地址: http://localhost:${PORT}`);
  console.log(`上传目录: ${UPLOAD_DIR}`);
  console.log(`下载目录: ${DOWNLOAD_DIR}`);
});

module.exports = app;
