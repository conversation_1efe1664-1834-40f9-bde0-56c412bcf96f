# Express 文件下载服务

一个基于 Express.js 的简单文件上传和下载服务，提供 Web 界面进行文件管理。

## 功能特性

- 📁 文件上传：支持多文件同时上传
- 📥 文件下载：直接点击下载文件
- 🗑️ 文件删除：删除不需要的文件
- 📊 文件信息：显示文件大小和修改时间
- 🌐 Web 界面：简洁易用的网页界面
- 🔒 安全限制：100MB 文件大小限制

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务
```bash
npm start
```

或者使用开发模式：
```bash
npm run dev
```

### 3. 访问应用
打开浏览器访问：http://localhost:3000

## 目录结构

```
serve-file/
├── index.js          # 主应用文件
├── package.json      # 项目配置
├── uploads/          # 临时上传目录（自动创建）
├── downloads/        # 文件存储目录（自动创建）
└── README.md         # 说明文档
```

## API 接口

### 获取文件列表
```
GET /api/files
```
返回所有可下载文件的列表，包含文件名、大小和修改时间。

### 上传文件
```
POST /upload
Content-Type: multipart/form-data
```
支持单个或多个文件上传。

### 下载文件
```
GET /download/:filename
```
下载指定文件名的文件。

### 删除文件
```
DELETE /api/files/:filename
```
删除指定的文件。

## 配置选项

可以通过环境变量配置：

- `PORT`: 服务端口（默认：3000）

示例：
```bash
PORT=8080 npm start
```

## 使用说明

1. **上传文件**：
   - 在首页点击"选择文件"按钮
   - 选择一个或多个文件
   - 点击"上传文件"按钮

2. **下载文件**：
   - 在文件列表中找到要下载的文件
   - 点击"下载"按钮

3. **删除文件**：
   - 在文件列表中找到要删除的文件
   - 点击"删除"按钮
   - 确认删除操作

## 技术栈

- **后端**: Node.js + Express.js
- **文件处理**: Multer + fs-extra
- **跨域支持**: CORS
- **前端**: 原生 HTML/CSS/JavaScript

## 安全注意事项

- 文件大小限制为 100MB
- 上传的文件会自动添加时间戳避免重名
- 建议在生产环境中添加身份验证
- 建议配置反向代理（如 Nginx）

## 许可证

MIT License
